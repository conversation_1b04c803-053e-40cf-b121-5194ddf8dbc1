import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:video_player/video_player.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/ui/widgets/screenshot_toggle_gesture.dart';

/// Enhanced version with additional customization options
class SplashScreenVideo extends StatefulWidget {
  final Widget? child;
  final Duration minDuration;
  final Duration maxDuration;
  final Duration fadeInDuration;
  final Duration fadeOutDuration;
  final Color backgroundColor;
  final VoidCallback? onVideoLoadFailed;
  final VoidCallback? onSplashComplete;

  const SplashScreenVideo({
    super.key,
    this.child,
    this.minDuration = const Duration(seconds: 2),
    this.maxDuration = const Duration(seconds: 3),
    this.fadeInDuration = const Duration(milliseconds: 700),
    this.fadeOutDuration = const Duration(milliseconds: 500),
    this.backgroundColor = Colors.black,
    this.onVideoLoadFailed,
    this.onSplashComplete,
  });

  @override
  State<SplashScreenVideo> createState() => _SplashScreenVideoState();
}

class _SplashScreenVideoState extends State<SplashScreenVideo> with SingleTickerProviderStateMixin {
  VideoPlayerController? _videoController;
  late AnimationController _animationController;

  bool _isVideoReady = false;
  bool _showVideo = false;
  bool _showApp = false;
  bool _disposed = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimation();
    _setupFullScreen();
    _startSplashSequence();
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      duration: widget.fadeOutDuration,
      vsync: this,
    );
  }

  void _setupFullScreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  }

  void _startSplashSequence() {
    _initializeVideo();
    _scheduleTransitions();
  }

  Future<void> _initializeVideo() async {
    try {
      _videoController = VideoPlayerController.asset(AssetPaths.splashScreenVdo);
      await _videoController!.initialize();

      if (!_disposed && mounted) {
        await _videoController!.setLooping(true);
        setState(() => _isVideoReady = true);
        _checkVideoTransition();
      }
    } catch (e) {
      logger.e('Video initialization failed: $e');
      widget.onVideoLoadFailed?.call();
    }
  }

  void _scheduleTransitions() {
    // Check for video transition after minimum duration
    Future.delayed(widget.minDuration, _checkVideoTransition);

    // Force app transition after maximum duration
    Future.delayed(widget.maxDuration, _transitionToApp);
  }

  void _checkVideoTransition() {
    if (!_disposed && mounted && _isVideoReady && !_showVideo && !_showApp) {
      _showVideoWithTransition();
    }
  }

  Future<void> _showVideoWithTransition() async {
    if (_disposed) return;

    setState(() => _showVideo = true);
    await _videoController?.play();
  }

  Future<void> _transitionToApp() async {
    if (_disposed || _showApp) return;

    await _animationController.forward();

    if (!_disposed && mounted) {
      setState(() => _showApp = true);
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      widget.onSplashComplete?.call();
    }
  }

  @override
  void dispose() {
    _disposed = true;
    _videoController?.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.backgroundColor,
      body: ScreenshotToggleGesture(
        child: AnimatedSwitcher(
          duration: widget.fadeOutDuration,
          child: _showApp ? _buildApp() : _buildSplash(),
        ),
      ),
    );
  }

  Widget _buildApp() {
    return ColoredBox(
      key: const ValueKey('app'),
      color: widget.backgroundColor,
      child: widget.child ?? const SizedBox.shrink(),
    );
  }

  Widget _buildSplash() {
    return ColoredBox(
      key: const ValueKey('splash'),
      color: widget.backgroundColor,
      child: _showVideo ? _buildVideoContent() : _buildStaticSplash(),
    );
  }

  Widget _buildStaticSplash() {
    return SizedBox.expand(
      child: Container(color: widget.backgroundColor),
    );
  }

  Widget _buildVideoContent() {
    if (_videoController == null || !_isVideoReady) {
      return _buildStaticSplash();
    }

    return AnimatedOpacity(
      opacity: _showVideo ? 1.0 : 0.0,
      duration: widget.fadeInDuration,
      child: SizedBox.expand(
        child: FittedBox(
          fit: BoxFit.cover,
          child: SizedBox(
            width: _videoController!.value.size.width,
            height: _videoController!.value.size.height,
            child: VideoPlayer(_videoController!),
          ),
        ),
      ),
    );
  }
}

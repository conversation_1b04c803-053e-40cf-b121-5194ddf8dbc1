// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:lottie/lottie.dart';
// import 'package:melodyze/core/ui/tokens/app_colors.dart';
// import 'package:melodyze/core/ui/tokens/asset_paths.dart';

// class SplashScreen extends StatefulWidget {
//   final Widget? child;

//   const SplashScreen({super.key, this.child});

//   @override
//   State<SplashScreen> createState() => _SplashScreenState();
// }

// class _SplashScreenState extends State<SplashScreen> with SingleTickerProviderStateMixin {
//   late AnimationController _fadeInController;
//   late Animation<double> _fadeInAnimation;
//   bool _showMainApp = false;

//   @override
//   void initState() {
//     super.initState();

//     // Set full screen mode for splash
//     SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

//     // Initialize animation controller
//     _fadeInController = AnimationController(
//       duration: const Duration(milliseconds: 800),
//       vsync: this,
//     );

//     _fadeInAnimation = Tween<double>(
//       begin: 0.0,
//       end: 1.0,
//     ).animate(CurvedAnimation(
//       parent: _fadeInController,
//       curve: Curves.easeInOut,
//     ));

//     // Start fade in animation
//     _fadeInController.forward();
//     _navigateToMainApp();
//   }

//   void _navigateToMainApp() {
//     Future.delayed(const Duration(milliseconds: 3000), () {
//       if (mounted) {
//         setState(() {
//           _showMainApp = true;
//         });
//         // Restore system UI after splash
//         SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
//       }
//     });
//   }

//   @override
//   void dispose() {
//     _fadeInController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: AppColors.black010101,
//       body: AnimatedSwitcher(
//         duration: const Duration(milliseconds: 600),
//         switchInCurve: Curves.easeInOut,
//         switchOutCurve: Curves.easeInOut,
//         transitionBuilder: (Widget child, Animation<double> animation) {
//           return FadeTransition(
//             opacity: animation,
//             child: child,
//           );
//         },
//         child: _showMainApp
//             ? ColoredBox(
//                 key: const ValueKey('main_app'),
//                 color: AppColors.black010101,
//                 child: widget.child ?? const SizedBox.shrink(),
//               )
//             : ColoredBox(
//                 key: const ValueKey('splash'),
//                 color: AppColors.black010101,
//                 child: SizedBox.expand(
//                   child: FadeTransition(
//                     opacity: _fadeInAnimation,
//                     child: FittedBox(
//                       fit: BoxFit.cover,
//                       alignment: Alignment.center,
//                       child: Lottie.asset(
//                         AssetPaths.splashScreenLottie,
//                         repeat: true,
//                         animate: true,
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//       ),
//     );
//   }
// }

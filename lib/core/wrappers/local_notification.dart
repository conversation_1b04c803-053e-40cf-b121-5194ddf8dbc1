import 'dart:convert';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:melodyze/core/services/file_manager/file_manager.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/share/cubit/fcm_notification_data_cubit.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

const AndroidNotificationChannel channel = AndroidNotificationChannel(
  'high_importance_channel',
  'High Importance Notifications',
  description: 'This channel is used for important notifications.',
  importance: Importance.high,
  playSound: true,
);

class LocalNotification {
  Future<void> init() async {
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
// initialise the plugin. app_icon needs to be a added as a drawable resource to the Android head project
    const AndroidInitializationSettings initializationSettingsAndroid = AndroidInitializationSettings('launch_background');

    const DarwinInitializationSettings initializationSettingsIOS = DarwinInitializationSettings();

    final InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    await flutterLocalNotificationsPlugin.initialize(initializationSettings, onDidReceiveNotificationResponse: onDidReceiveNotificationResponse);

    await flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()?.createNotificationChannel(channel);
    logger.d("LocalNotification.init");
    // final NotificationAppLaunchDetails? notificationAppLaunchDetails =
    // await flutterLocalNotificationsPlugin.getNotificationAppLaunchDetails();
  }

  void onDidReceiveNotificationResponse(NotificationResponse notificationResponse) async {
    final String? payload = notificationResponse.payload;
    if (notificationResponse.payload != null) {
      DI().resolve<FcmNotificationDataCubit>().setNotificationData(jsonDecode(payload!));
    }
  }

  Future<void> showForegroundNotification(RemoteMessage message) async {
    logger.d("showForegroundNotification");

    BigPictureStyleInformation? bigPictureStyleInformation;

    final RemoteNotification? notification = message.notification;

    if (notification?.android?.imageUrl != null) {
      final result = await FileManagerQuickAccess.download(
        notification!.android!.imageUrl!,
        FileType.images,
      );

      if (result is FileOperationError) {
        logger.e("Failed to download image: ${result.message}");
        return;
      }

      final imagePath = (result as FileOperationSuccess).localPath;
      File imageFile = File(imagePath);
      List<int> imageFileBytes = await imageFile.readAsBytes();
      String base64Image = base64Encode(imageFileBytes);
      bigPictureStyleInformation = BigPictureStyleInformation(ByteArrayAndroidBitmap.fromBase64String(base64Image));
    }

    final AndroidNotificationDetails androidPlatformChannelSpecifics = AndroidNotificationDetails(
      channel.id,
      channel.name,
      channelDescription: channel.description,
      playSound: true,
      styleInformation: bigPictureStyleInformation,
      icon: '@mipmap/launcher_icon',
    );

    final NotificationDetails platformChannelSpecifics = NotificationDetails(android: androidPlatformChannelSpecifics);

    await flutterLocalNotificationsPlugin.show(
      notification.hashCode,
      notification?.title,
      notification?.body,
      platformChannelSpecifics,
      payload: jsonEncode(message.data),
    );
  }
}

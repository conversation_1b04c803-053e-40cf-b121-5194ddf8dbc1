import 'dart:convert';

import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/device_info_wrapper.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/secure_storage_helper.dart';
import 'package:melodyze/modules/auth/model/user.dart';

class UserDataManager {
  final SecureStorageHelper _secureStorageHelper = DI().resolve<SecureStorageHelper>();

  Future<void> saveUserDataOnAppInit() async {
    try {
      final deviceInfo = await DeviceInfoWrapper.getSystemInfo();
      await _saveDeviceInfo(deviceInfo);

      final userData = await _secureStorageHelper.read(SecureStorageKeys.user);

      if (userData != null && userData.isNotEmpty) {
        final user = User.fromJson(jsonDecode(userData));
        final updatedUser = user.copyWith(deviceInfo: deviceInfo);
        await _saveUserData(updatedUser);
        logger.i('UserDataManager: Updated logged-in user data with fresh device info');
      } else {
        logger.i('UserDataManager: Saved device info for non-authenticated user');
      }
    } catch (e) {
      logger.e('UserDataManager: Error saving user data on app init', error: e);
    }
  }

  Future<void> saveUserData(User user) async {
    try {
      final deviceInfo = await DeviceInfoWrapper.getSystemInfo();
      final updatedUser = user.copyWith(deviceInfo: deviceInfo);
      await _saveUserData(updatedUser);
      await _saveDeviceInfo(deviceInfo);
      logger.i('UserDataManager: Saved complete user data');
    } catch (e) {
      logger.e('UserDataManager: Error saving user data', error: e);
    }
  }

  Future<User?> getUserData() async {
    try {
      final userData = await _secureStorageHelper.read(SecureStorageKeys.user);
      if (userData != null && userData.isNotEmpty) {
        return User.fromJson(jsonDecode(userData));
      }
      return null;
    } catch (e) {
      logger.e('UserDataManager: Error getting user data', error: e);
      return null;
    }
  }

  Future<UserDeviceInfo?> getDeviceInfo() async {
    try {
      final deviceInfoData = await _secureStorageHelper.read(SecureStorageKeys.deviceInfo);
      if (deviceInfoData != null && deviceInfoData.isNotEmpty) {
        return UserDeviceInfo.fromJson(jsonDecode(deviceInfoData));
      }
      return null;
    } catch (e) {
      logger.e('UserDataManager: Error getting device info', error: e);
      return null;
    }
  }

  Future<bool> isUserLoggedIn() async {
    try {
      final userData = await _secureStorageHelper.read(SecureStorageKeys.user);
      return userData != null && userData.isNotEmpty;
    } catch (e) {
      logger.e('UserDataManager: Error checking login status', error: e);
      return false;
    }
  }

  Future<void> clearUserData() async {
    try {
      await _secureStorageHelper.delete(SecureStorageKeys.user);
      await _secureStorageHelper.delete(SecureStorageKeys.deviceInfo);
      logger.i('UserDataManager: Cleared all user data');
    } catch (e) {
      logger.e('UserDataManager: Error clearing user data', error: e);
    }
  }

  Future<void> _saveUserData(User user) async {
    await _secureStorageHelper.write(SecureStorageKeys.user, jsonEncode(user.toJson()));
  }

  Future<void> _saveDeviceInfo(UserDeviceInfo deviceInfo) async {
    await _secureStorageHelper.write(SecureStorageKeys.deviceInfo, jsonEncode(deviceInfo.toJson()));
  }
}

import 'dart:io';
import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/services/file_manager/models/cache_models.dart';
import 'package:melodyze/core/services/file_manager/models/file_operation_models.dart';
import 'package:melodyze/core/services/file_manager/utils/cache_storage.dart';
import 'package:melodyze/core/services/file_manager/utils/retry_utils.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/injector.dart';

class FileManagerService {
  static FileManagerService? _instance;
  static FileManagerService get instance => _instance ??= FileManagerService._();

  FileManagerService._();

  late final CacheStorage _cacheStorage;
  late final ApiClient _apiClient;
  final Map<FileType, CachePolicy> _cachePolicies = {};
  bool _initialized = false;

  Future<void> initialize() async {
    if (_initialized) return;

    _cacheStorage = FlutterCacheManagerStorage();
    await _cacheStorage.initialize();

    _apiClient = DI().resolve<ApiClient>();

    for (final fileType in FileType.values) {
      _cachePolicies[fileType] = fileType.defaultCachePolicy;
    }

    _initialized = true;
    logger.d('FileManagerService initialized');
  }

  Future<FileOperationResult> downloadFile(DownloadRequest request) async {
    await _ensureInitialized();
    try {
      if (request.config.useCache && !request.config.forceRefresh) {
        final flutterStorage = _flutterStorage!;
        final cachedPath = await flutterStorage.getCachedFilePath(request.cacheKey, request.fileType);
        if (cachedPath != null) {
          final cachedFile = File(cachedPath);
          if (cachedFile.existsSync()) {
            logger.d('File served from cache: ${request.url}');
            return FileOperationSuccess(
              localPath: cachedPath,
              fromCache: true,
              sizeBytes: await cachedFile.length(),
            );
          }
        }
      }

      return await RetryUtils.executeWithRetry(
        () => _downloadFromRemote(request),
        maxRetries: request.config.maxRetries,
        shouldRetry: RetryUtils.shouldRetryNetworkOperation,
        operationName: 'Download ${request.url}',
      );
    } catch (e, stackTrace) {
      logger.e('Error downloading file: ${request.url}', error: e, stackTrace: stackTrace);
      return FileOperationErrorHandler.handleError(
        e,
        stackTrace,
        'Download file',
        context: {'url': request.url, 'fileType': request.fileType.name},
      );
    }
  }

  Future<FileOperationResult> uploadFile(UploadRequest request) async {
    await _ensureInitialized();

    try {
      // TODO: Handle this
      // request.onProgress?.call(FileOperationProgress.uploading());

      final inputFile = File(request.localPath);
      if (!inputFile.existsSync()) {
        return const FileOperationError(
          message: 'Input file does not exist',
          errorCode: 'FILE_NOT_FOUND',
        );
      }

      final uploadFilename = _generateUploadFilename(request.localPath, request.customFilename);
      final contentType = FileUtils.getContentTypeFromFilePath(request.localPath);

      final uploadResponse = await _apiClient.put(
        request.uploadEndpoint,
        body: {
          'filename': uploadFilename,
          'mimetype': contentType,
        },
      );

      final uploadResponseData = uploadResponse?['data'] as Map<String, dynamic>?;
      final putSignedUrl = uploadResponseData?['url'] as String?;
      final uploadPath = uploadResponseData?['path'] as String?;

      if (putSignedUrl == null || uploadPath == null) {
        return const FileOperationError(
          message: 'Upload URL or path missing in server response',
          errorCode: 'INVALID_RESPONSE',
        );
      }

      final fileBytes = await inputFile.readAsBytes();
      final totalBytes = fileBytes.length;

      request.onProgress?.call(FileOperationProgress.uploading(
        totalBytes: totalBytes,
        transferredBytes: 0,
      ));

      await _uploadBytes(putSignedUrl, fileBytes, contentType, request.onProgress);

      logger.d('File uploaded successfully: ${request.localPath} -> $uploadPath');
      return FileOperationSuccess(
        localPath: uploadPath,
        fromCache: false,
        sizeBytes: totalBytes,
      );
    } catch (e, stackTrace) {
      logger.e('Error uploading file: ${request.localPath}', error: e, stackTrace: stackTrace);
      return FileOperationErrorHandler.handleError(
        e,
        stackTrace,
        'Upload file',
        context: {'localPath': request.localPath, 'uploadEndpoint': request.uploadEndpoint},
      );
    }
  }

  Future<FileOperationResult> _downloadFromRemote(DownloadRequest request) async {
    try {
      request.onProgress?.call(FileOperationProgress.downloading(
        totalBytes: 0,
        transferredBytes: 0,
      ));

      // Use flutter_cache_manager for download and caching
      final flutterStorage = _flutterStorage!;
      final file = await flutterStorage.downloadFile(request.url, request.cacheKey, request.fileType);

      if (file == null) {
        return const FileOperationError(
          message: 'Download failed: Could not download file',
          errorCode: 'DOWNLOAD_FAILED',
        );
      }

      final fileSize = await file.length();

      request.onProgress?.call(FileOperationProgress.downloading(
        totalBytes: fileSize,
        transferredBytes: fileSize,
      ));

      logger.d('File downloaded successfully: ${request.url}');
      return FileOperationSuccess(
        localPath: file.path,
        fromCache: false, // flutter_cache_manager handles cache internally
        sizeBytes: fileSize,
      );
    } catch (e) {
      logger.e('Error in _downloadFromRemote: ${request.url}', error: e);
      rethrow;
    }
  }

  static String getCacheKeyFromUrl(String url) {
    final uri = Uri.parse(url);
    final upath = uri.path;
    final uhost = uri.host;
    return '$uhost$upath';
  }

  String _generateUploadFilename(String originalPath, String? customFilename) {
    final fileExt = FileUtils.extractFileExtension(originalPath).extension;
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    if (customFilename?.isNotEmpty == true) {
      final sanitizedFilename = customFilename!.replaceAll(RegExp(r'[^\w\-_\.]'), '_').replaceAll(RegExp(r'_+'), '_');
      return '${sanitizedFilename}_$timestamp$fileExt';
    }

    return '$timestamp$fileExt';
  }

  Future<void> _ensureInitialized() async {
    if (!_initialized) {
      await initialize();
    }
  }

  Future<void> _uploadBytes(
    String signedUrl,
    List<int> bytes,
    String contentType,
    FileOperationProgressCallback? onProgress,
  ) async {
    final request = await HttpClient().putUrl(Uri.parse(signedUrl));
    request.headers.set('Content-Type', contentType);
    request.headers.set('Content-Length', bytes.length.toString());

    request.add(bytes);

    final response = await request.close();
    if (response.statusCode != 200) {
      throw Exception('Upload failed with status: ${response.statusCode}');
    }

    onProgress?.call(FileOperationProgress.uploading(
      totalBytes: bytes.length,
      transferredBytes: bytes.length,
    ));
  }

  void updateCachePolicy(FileType fileType, CachePolicy policy) {
    _cachePolicies[fileType] = policy;
    logger.d('Updated cache policy for ${fileType.name}: $policy');
  }

  CachePolicy getCachePolicy(FileType fileType) {
    return _cachePolicies[fileType] ?? fileType.defaultCachePolicy;
  }

  Future<bool> isFileInCache(String key, FileType fileType) async {
    await _ensureInitialized();

    final flutterStorage = _flutterStorage!;
    return await flutterStorage.isFileInCache(key, fileType);
  }

  Future<String?> getCachedFilePath(String url, FileType fileType) async {
    await _ensureInitialized();

    final flutterStorage = _flutterStorage!;
    return await flutterStorage.getCachedFilePath(url, fileType);
  }

  Future<void> dispose() async {
    await _cacheStorage.dispose();
    _initialized = false;
    logger.d('FileManagerService disposed');
  }

  FlutterCacheManagerStorage? get _flutterStorage {
    return _cacheStorage is FlutterCacheManagerStorage ? _cacheStorage : null;
  }
}

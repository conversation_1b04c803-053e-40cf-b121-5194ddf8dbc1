import 'dart:io';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:melodyze/core/services/file_manager/file_manager.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/local_storage_helper.dart';
import 'package:melodyze/core/wrappers/injector.dart';

abstract class CacheStorage {
  Future<void> initialize();
  Future<CachedFileEntry?> getCachedFile(String url, FileType fileType);
  Future<void> clearCacheByType(FileType fileType);
  Future<void> clearAllCache();
  Future<String?> getCachedFilePath(String key, FileType fileType);
  Future<void> dispose();
}

class FlutterCacheManagerStorage implements CacheStorage {
  static const String _statsKeyPrefix = 'cache_stats_';

  late final LocalStorageHelper _storage;
  final Map<FileType, CacheManager> _cacheManagers = {};
  bool _initialized = false;

  @override
  Future<void> initialize() async {
    if (_initialized) return;

    _storage = DI().resolve<LocalStorageHelper>();

    // Create cache managers for each file type with their specific policies
    for (final fileType in FileType.values) {
      final policy = fileType.defaultCachePolicy;
      _cacheManagers[fileType] = CacheManager(
        Config(
          'melodyze_${fileType.name}',
          stalePeriod: Duration(hours: policy.maxAgeHours),
          maxNrOfCacheObjects: policy.maxFiles,
          repo: JsonCacheInfoRepository(databaseName: 'melodyze_${fileType.name}'),
          fileService: HttpFileService(),
        ),
      );
    }

    _initialized = true;
    logger.d('FlutterCacheManagerStorage initialized');
  }

  @override
  Future<CachedFileEntry?> getCachedFile(String url, FileType fileType) async {
    await _ensureInitialized();

    try {
      final cacheManager = _cacheManagers[fileType]!;
      final fileInfo = await cacheManager.getFileFromCache(url);

      if (fileInfo == null) return null;

      final entry = CachedFileEntry(
        id: FileManagerService.getCacheKeyFromUrl(url),
        originalUrl: url,
        localPath: fileInfo.file.path,
        fileType: fileType.name,
        cachedAt: fileInfo.validTill.subtract(Duration(hours: fileType.defaultCachePolicy.maxAgeHours)),
        lastAccessedAt: DateTime.now(),
        etag: null, // FileInfo doesn't expose eTag directly
        contentType: null, // Not available in FileInfo
      );

      return entry;
    } catch (e) {
      logger.e('Error getting cached file for $url', error: e);
      return null;
    }
  }

  @override
  Future<void> clearCacheByType(FileType fileType) async {
    await _ensureInitialized();

    final cacheManager = _cacheManagers[fileType]!;
    await cacheManager.emptyCache();

    // Clear statistics
    await _storage.remove('$_statsKeyPrefix${fileType.name}');

    logger.d('Cleared cache for file type: ${fileType.name}');
  }

  @override
  Future<void> clearAllCache() async {
    await _ensureInitialized();

    for (final fileType in FileType.values) {
      await clearCacheByType(fileType);
    }

    logger.d('Cleared all cache');
  }

  Future<File?> downloadFile(String url, String cacheKey, FileType fileType) async {
    await _ensureInitialized();
    try {
      final cacheManager = _cacheManagers[fileType]!;
      final file = await cacheManager.getSingleFile(url, key: cacheKey);
      return file;
    } catch (e) {
      logger.e('Error downloading file with flutter_cache_manager: $url', error: e);
      return null;
    }
  }

  Future<bool> isFileInCache(String key, FileType fileType) async {
    await _ensureInitialized();

    try {
      final cacheManager = _cacheManagers[fileType]!;
      final fileInfo = await cacheManager.getFileFromCache(key);
      return fileInfo != null;
    } catch (e) {
      logger.e('Error checking cache for: $key', error: e);
      return false;
    }
  }

  @override
  Future<String?> getCachedFilePath(String key, FileType fileType) async {
    await _ensureInitialized();

    try {
      final cacheManager = _cacheManagers[fileType]!;
      final fileInfo = await cacheManager.getFileFromCache(key);
      return fileInfo?.file.path;
    } catch (e) {
      logger.e('Error getting cached file path for: $key', error: e);
      return null;
    }
  }

  @override
  Future<void> dispose() async {
    _initialized = false;
    logger.d('FlutterCacheManagerStorage disposed');
  }

  Future<void> _ensureInitialized() async {
    if (!_initialized) {
      await initialize();
    }
  }
}

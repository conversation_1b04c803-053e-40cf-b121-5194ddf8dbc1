library;

import 'package:melodyze/core/services/file_manager/file_manager_service.dart';
import 'package:melodyze/core/services/file_manager/models/file_operation_models.dart';

export 'file_manager_service.dart';
export 'models/file_operation_models.dart';
export 'models/cache_models.dart';
export 'utils/progress_tracker.dart';
export 'utils/retry_utils.dart';
export 'utils/cache_storage.dart';

Future<void> initializeFileManager() async {
  final fileManager = FileManagerService.instance;
  await fileManager.initialize();
}

Future<void> disposeFileManager() async {
  final fileManager = FileManagerService.instance;
  await fileManager.dispose();
}

class FileManagerQuickAccess {
  FileManagerQuickAccess._();

  static FileManagerService get fileManager => FileManagerService.instance;

  static Future<FileOperationResult> download(
    String url,
    FileType fileType, {
    FileOperationProgressCallback? onProgress,
    bool useCache = true,
    bool forceRefresh = false,
  }) async {
    final request = DownloadRequest(
      cacheKey: FileManagerService.getCacheKeyFromUrl(url),
      url: url,
      fileType: fileType,
      config: FileOperationConfig(
        useCache: useCache,
        forceRefresh: forceRefresh,
      ),
      onProgress: onProgress,
    );

    return await fileManager.downloadFile(request);
  }

  static Future<FileOperationResult> upload(
    String localPath,
    String uploadEndpoint,
    FileType fileType, {
    FileOperationProgressCallback? onProgress,
    String? customFilename,
  }) async {
    final request = UploadRequest(
      localPath: localPath,
      uploadEndpoint: uploadEndpoint,
      fileType: fileType,
      onProgress: onProgress,
      customFilename: customFilename,
    );

    return await fileManager.uploadFile(request);
  }

  static Future<bool> isFileCached(String url, FileType fileType) async {
    final cacheKey = FileManagerService.getCacheKeyFromUrl(url);
    return await fileManager.isFileInCache(cacheKey, fileType);
  }

  static Future<void> clearCache([FileType? fileType]) async {
    // TODO implement clear cache
    // if (fileType != null) {
    //   await fileManager.clearCacheByType(fileType);
    // } else {
    //   await fileManager.clearAllCache();
    // }
  }

}

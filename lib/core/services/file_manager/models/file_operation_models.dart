import 'package:melodyze/core/services/file_manager/file_manager.dart';

enum FileType { images, bgm, guide, bgmWav, songThumbnail, artistThumbnail, lyricsJson, others }

extension FileTypeExtension on FileType {
  String get directoryName => name;

  CachePolicy get defaultCachePolicy {
    switch (this) {
      case FileType.images:
      case FileType.songThumbnail:
      case FileType.artistThumbnail:
        return CachePolicy(
          maxFiles: 500,
          maxSizeBytes: 10 * 1024 * 1024, // 50MB
          maxAgeHours: 24 * 7, // 1 week
        );
      case FileType.bgm:
      case FileType.guide:
        return CachePolicy(
          maxFiles: 100,
          maxSizeBytes: 200 * 1024 * 1024, // 200MB
          maxAgeHours: 24 * 30, // 1 month
        );
      case FileType.bgmWav:
        return CachePolicy(
          maxFiles: 10,
          maxSizeBytes: 200 * 1024 * 1024, // 200MB
          maxAgeHours: 24 * 30, // 1 month
        );
      case FileType.lyricsJson:
        return CachePolicy(
          maxFiles: 500,
          maxSizeBytes: 5 * 1024 * 1024, // 5MB
          maxAgeHours: 24 * 30, // 1 month
        );
      case FileType.others:
        return CachePolicy(
          maxFiles: 100,
          maxSizeBytes: 50 * 1024 * 1024, // 50MB
          maxAgeHours: 24 * 7, // 1 week
        );
    }
  }
}

sealed class FileOperationResult {
  const FileOperationResult();
}

final class FileOperationSuccess extends FileOperationResult {
  final String localPath;
  final bool fromCache;
  final int sizeBytes;

  const FileOperationSuccess({
    required this.localPath,
    required this.fromCache,
    required this.sizeBytes,
  });

  @override
  String toString() {
    return 'FileOperationSuccess(localPath: $localPath, fromCache: $fromCache,sizeBytes: $sizeBytes)';
  }
}

final class FileOperationError extends FileOperationResult {
  final String message;
  final String? errorCode;
  final Exception? originalException;

  const FileOperationError({
    required this.message,
    this.errorCode,
    this.originalException,
  });

  @override
  String toString() {
    return 'FileOperationError(message: $message, errorCode: $errorCode)';
  }
}

class FileOperationProgress {
  final int totalBytes;
  final int transferredBytes;
  final double progress;
  final double? speedBytesPerSecond;
  final int? estimatedSecondsRemaining;
  final FileOperationPhase phase;

  const FileOperationProgress({
    required this.totalBytes,
    required this.transferredBytes,
    required this.progress,
    required this.phase,
    this.speedBytesPerSecond,
    this.estimatedSecondsRemaining,
  });

  factory FileOperationProgress.downloading({
    required int totalBytes,
    required int transferredBytes,
    double? speedBytesPerSecond,
    int? estimatedSecondsRemaining,
  }) {
    return FileOperationProgress(
      totalBytes: totalBytes,
      transferredBytes: transferredBytes,
      progress: totalBytes > 0 ? transferredBytes / totalBytes : 0.0,
      phase: FileOperationPhase.downloading,
      speedBytesPerSecond: speedBytesPerSecond,
      estimatedSecondsRemaining: estimatedSecondsRemaining,
    );
  }

  factory FileOperationProgress.uploading({
    required int totalBytes,
    required int transferredBytes,
    double? speedBytesPerSecond,
    int? estimatedSecondsRemaining,
  }) {
    return FileOperationProgress(
      totalBytes: totalBytes,
      transferredBytes: transferredBytes,
      progress: totalBytes > 0 ? transferredBytes / totalBytes : 0.0,
      phase: FileOperationPhase.uploading,
      speedBytesPerSecond: speedBytesPerSecond,
      estimatedSecondsRemaining: estimatedSecondsRemaining,
    );
  }

  String get formattedSpeed {
    if (speedBytesPerSecond == null) return 'Unknown';
    final speed = speedBytesPerSecond!;
    if (speed < 1024) return '${speed.toStringAsFixed(0)} B/s';
    if (speed < 1024 * 1024) return '${(speed / 1024).toStringAsFixed(1)} KB/s';
    return '${(speed / (1024 * 1024)).toStringAsFixed(1)} MB/s';
  }

  String get formattedETA {
    if (estimatedSecondsRemaining == null) return 'Unknown';
    final seconds = estimatedSecondsRemaining!;
    if (seconds < 60) return '${seconds}s';
    if (seconds < 3600) return '${(seconds / 60).ceil()}m';
    return '${(seconds / 3600).ceil()}h';
  }

  @override
  String toString() {
    return 'FileOperationProgress(progress: ${(progress * 100).toStringAsFixed(1)}%, '
        'phase: $phase, speed: $formattedSpeed, ETA: $formattedETA)';
  }
}

enum FileOperationPhase { initializing, downloading, uploading, completed}

class FileOperationConfig {
  final bool useCache;
  final bool forceRefresh;
  final int maxRetries;
  final int timeoutSeconds;
  final Map<String, String>? headers;
  final String? customFilename;

  const FileOperationConfig({
    this.useCache = true,
    this.forceRefresh = false,
    this.maxRetries = 3,
    this.timeoutSeconds = 30,
    this.headers,
    this.customFilename,
  });

  FileOperationConfig copyWith({
    bool? useCache,
    bool? forceRefresh,
    int? maxRetries,
    int? timeoutSeconds,
    Map<String, String>? headers,
    String? customFilename,
  }) {
    return FileOperationConfig(
      useCache: useCache ?? this.useCache,
      forceRefresh: forceRefresh ?? this.forceRefresh,
      maxRetries: maxRetries ?? this.maxRetries,
      timeoutSeconds: timeoutSeconds ?? this.timeoutSeconds,
      headers: headers ?? this.headers,
      customFilename: customFilename ?? this.customFilename,
    );
  }

  @override
  String toString() {
    return 'FileOperationConfig(useCache: $useCache, forceRefresh: $forceRefresh, '
        'maxRetries: $maxRetries, timeoutSeconds: $timeoutSeconds)';
  }
}

typedef FileOperationProgressCallback = void Function(FileOperationProgress progress);

class DownloadRequest {
  final String url;
  final String cacheKey;
  final FileType fileType;
  final FileOperationConfig config;
  final FileOperationProgressCallback? onProgress;

  const DownloadRequest({
    required this.url,
    required this.cacheKey,
    required this.fileType,
    this.config = const FileOperationConfig(),
    this.onProgress,
  });

  @override
  String toString() {
    return 'DownloadRequest(url: $url, fileType: $fileType, config: $config)';
  }
}

class UploadRequest {
  final String localPath;
  final String uploadEndpoint;
  final FileType fileType;
  final FileOperationConfig config;
  final FileOperationProgressCallback? onProgress;
  final String? customFilename;

  const UploadRequest({
    required this.localPath,
    required this.uploadEndpoint,
    required this.fileType,
    this.config = const FileOperationConfig(),
    this.onProgress,
    this.customFilename,
  });

  @override
  String toString() {
    return 'UploadRequest(localPath: $localPath, uploadEndpoint: $uploadEndpoint, '
        'fileType: $fileType, customFilename: $customFilename)';
  }
}

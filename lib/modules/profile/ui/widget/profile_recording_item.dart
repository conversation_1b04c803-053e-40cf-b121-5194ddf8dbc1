import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/atom/marquee.dart';
import 'package:melodyze/core/ui/molecules/dialoges.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/modules/profile/bloc/profile_event.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';

enum RecordingListType { practiceRecording, finalRecording }

class RecordingListItem extends StatelessWidget {
  final RecordingModel recording;
  final ValueChanged<RecordingModel>? onAction;
  final RecordingListType type;

  const RecordingListItem({
    super.key,
    required this.recording,
    this.onAction,
    required this.type,
  });

  String get recordingInfo => '${recording.genre} · ${Config.keyMapShort[recording.scale] ?? recording.scale} · ${recording.tempo} bpm';

  String get title => FileUtils.fromSnakeCase(recording.title.split('-').first);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        context.pushRoute(
          VideoPlayerReelRoute(
            recordings: [recording],
            showMenubar: true,
            isFinalRecording: recording.isFinalSave,
            disableVideo: true,
          ),
        );
      },
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: SizedBox(
              height: 44,
              width: 44,
              child: ImageLoader.cachedNetworkImage(recording.thumbnailPath),
            ),
          ),
          const SizedBox(
            width: 16,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Marqueee(
                  text: type == RecordingListType.practiceRecording ? recordingInfo : title,
                  style: AppTextStyles.text16semiBold.copyWith(
                    fontFamily: AppFonts.inter,
                  ),
                  width: 320,
                ),
                const SizedBox(
                  height: 8,
                ),
                Marqueee(
                  text: type == RecordingListType.practiceRecording ? TimeUtils.formatEpochMilliseconds(recording.createdAt) : recordingInfo,
                  style: AppTextStyles.text12regular.copyWith(
                    fontFamily: AppFonts.iceland,
                    color: AppColors.greySlider,
                  ),
                  width: 300,
                ),
              ],
            ),
          ),
          PopupMenuButton(
            key: ObjectKey(recording.id),
            color: Colors.transparent,
            elevation: 0,
            itemBuilder: (context) => [
              PopupMenuItem(
                padding: EdgeInsets.zero,
                child: AppGradientContainer(
                  gradient: AppGradients.gradientBlackTeal,
                  child: Column(
                    children: [
                      PopupMenuItem(
                        child: ListTile(
                          onTap: () async {
                            final result = await showYesNoDialog(
                              context: context,
                              title: type == RecordingListType.practiceRecording ? 'Make it final' : 'Move to practice',
                            );
                            if (result && context.mounted) {
                              DI().resolve<ProfileBloc>().add(
                                    ProfileMarkAsFinalEvent(
                                      recordingId: recording.id,
                                      isFinal: type == RecordingListType.finalRecording,
                                    ),
                                  );
                              onAction?.call(recording);
                              Navigator.pop(context);
                            }
                          },
                          leading: const Icon(
                            Icons.check,
                            color: AppColors.white,
                          ),
                          title: Text(
                            type == RecordingListType.practiceRecording ? 'Make it final' : 'Move to practice',
                            style: AppTextStyles.text16medium.copyWith(
                              fontFamily: AppFonts.iceland,
                              color: AppColors.white,
                            ),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: ImageLoader.fromAsset(AssetPaths.gradientdivider),
                      ),
                      PopupMenuItem(
                          child: ListTile(
                        leading: Icon(
                          Icons.delete_forever,
                          color: AppColors.white,
                        ),
                        title: Text(
                          'Delete',
                          style: AppTextStyles.text16medium.copyWith(
                            fontFamily: AppFonts.iceland,
                            color: AppColors.white,
                          ),
                        ),
                        onTap: () async {
                          final result = await showYesNoDialog(context: context, title: 'Delete recording');
                          if (result && context.mounted) {
                            DI().resolve<ProfileBloc>().add(DeleteRecordingEvent(recording.id));
                            onAction?.call(recording);
                            Navigator.pop(context);
                          }
                        },
                      )),
                    ],
                  ),
                ),
              ),
            ],
            child: Padding(
              padding: const EdgeInsets.only(right: 12.0),
              child: ShaderMask(
                shaderCallback: (bounds) => AppGradients.gradientPinkIcon.createShader(bounds),
                child: const Icon(
                  Icons.more_vert,
                  size: 28,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

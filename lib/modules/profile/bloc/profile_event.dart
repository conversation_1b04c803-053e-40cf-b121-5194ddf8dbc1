import 'package:melodyze/core/generic_bloc/events.dart';

class ProfileLoadDataEvent extends BaseEvent {
  final bool showCurrentRecording;
  const ProfileLoadDataEvent({this.showCurrentRecording = false});

  @override
  List<Object> get props => [];
}

class DeleteRecordingEvent extends BaseEvent {
  final String recordingId;
  const DeleteRecordingEvent(this.recordingId);

  @override
  List<Object?> get props => [recordingId];
}

class DeleteAccountEvent extends BaseEvent {
  const DeleteAccountEvent();

  @override
  List<Object?> get props => [];
}

class LogoutEvent extends BaseEvent {
  const LogoutEvent();

  @override
  List<Object?> get props => [];
}

class ProfileMarkAsFinalEvent extends BaseEvent {
  final String recordingId;
  final bool isFinal;
  const ProfileMarkAsFinalEvent({
    required this.recordingId,
    required this.isFinal,
  });

  @override
  List<Object?> get props => [recordingId];
}

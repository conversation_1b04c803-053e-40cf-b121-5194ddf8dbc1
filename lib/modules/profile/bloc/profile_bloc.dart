import 'dart:async';

import 'package:flutter/material.dart';
import 'package:melodyze/core/api_client/api_exception.dart';
// import 'package:melodyze/core/services/file_manager/file_manager.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/ui/molecules/dialoges.dart';
import 'package:melodyze/modules/profile/bloc/profile_event.dart';
import 'package:melodyze/modules/profile/bloc/profile_state.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/profile/profile_screen.dart';
import 'package:melodyze/modules/profile/repo/profile_repo.dart';

class ProfileBloc extends SafeBloc {
  final ProfileRepo profileRepo;
  List<RecordingModel> recordings = [];
  ProfileBloc({required this.profileRepo}) : super(InitialState()) {
    on<ProfileLoadDataEvent>(_loadProfileData);
    on<DeleteRecordingEvent>(_deleteRecording);
    on<DeleteAccountEvent>(_deleteAccount);
    on<LogoutEvent>(_logout);
    on<ProfileMarkAsFinalEvent>(_markAsFinal);
    add(const ProfileLoadDataEvent());
  }

  ProfileRecordingsUiModel prepareUiModel(bool showCurrentRecording) {
    final practiceRecordings = recordings.where((element) => !element.isFinalSave).toList();
    return ProfileRecordingsUiModel(
      finalRecordings: recordings.where((element) => element.isFinalSave).toList(),
      practiceRecordings: {
        for (final recording in practiceRecordings) recording.masterSongId: practiceRecordings.where((element) => element.masterSongId == recording.masterSongId).toList(),
      },
      showCurrentRecording: showCurrentRecording,
    );
  }

  FutureOr<void> _loadProfileData(ProfileLoadDataEvent event, _) async {
    emit(LoadingState());
    final response = await profileRepo.getRecordings();

    if (!response.isSuccess) {
      emit(BlocFailureState(response.error));
      return;
    }
    recordings = response.data;

    emit(BlocSuccessState<ProfileRecordingsUiModel>(prepareUiModel(event.showCurrentRecording)));
  }

  FutureOr<void> _deleteRecording(DeleteRecordingEvent event, _) async {
    final response = await profileRepo.deleteRecording(event.recordingId);
    if (!response.isSuccess) {
      emit(BlocFailureState(response.error));
      return;
    }

    if (recordings.isEmpty) {
      add(const ProfileLoadDataEvent());
      return;
    }
    List<RecordingModel> tempRecordings = List.from(recordings);
    tempRecordings.removeWhere((element) => element.id == event.recordingId);
    recordings = tempRecordings;
    emit(BlocSuccessState<ProfileRecordingsUiModel>(prepareUiModel(false)));
  }

  FutureOr<void> _deleteAccount(DeleteAccountEvent event, _) async {
    emit(LoadingState());
    final response = await profileRepo.deleteAccount();
    if (!response.isSuccess) {
      emit(BlocFailureState(response.error));
      return;
    }
    recordings.clear();
    emit(const AccountDeletedState());
  }

  void _logout(LogoutEvent event, _) async {
    recordings.clear();
    emit(const LogOutClickedState());
  }

  Future<void> handleMenubarActions(BuildContext context, String action) async {
    switch (action) {
      case 'clear_cache':
        final result = await showYesNoDialog(
          context: context,
          title: 'Clear cache',
          subTitle: 'Are you sure you want to clear cache ?',
        );
        if (result && context.mounted) {
          // await FileManagerQuickAccess.clearCache();
        }
        break;
      case 'logout':
        final result = await showYesNoDialog(
          context: context,
          title: 'Logout',
          subTitle: 'Are you sure you want to logout ?',
        );
        if (result && context.mounted) {
          add(const LogoutEvent());
        }
        break;
      case 'delete_account':
        final result = await showYesNoDialog(
          context: context,
          title: 'Delete Account',
          subTitle: 'Are you sure you want to delete your account ?',
        );
        if (result && context.mounted) {
          add(const DeleteAccountEvent());
        }
        break;
    }
  }

  Future<void> _markAsFinal(ProfileMarkAsFinalEvent event, _) async {
    final response = await profileRepo.saveAsFinal(event.recordingId);
    if (response.isSuccess) {
      emit(ProfileSnackBarState(message: event.isFinal ? 'Recording marked as practice!' : 'Recording marked as final!'));
      add(const ProfileLoadDataEvent());
    } else {
      emit(ProfileSnackBarState(
        message: 'Failed to mark as final: ${(response.error.e as ApiException).message}',
        isError: true,
      ));
      return;
    }
  }
}

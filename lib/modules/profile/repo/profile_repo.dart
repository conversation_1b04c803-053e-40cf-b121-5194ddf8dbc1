import 'package:melodyze/core/generic_arch/base_repository.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/profile/serivce/profile_service.dart';

class ProfileRepo extends BaseRepo {
  final ProfileService profileService;
  ProfileRepo({required this.profileService});

  Future<RepoResult> getRecordings() async {
    return await executeAndReturnResult(
      () => profileService.getRecordings(),
      (Map<String, dynamic> json) async => RepoResult.success(
        ((json['data'] as Map<String, dynamic>)['recordings'] as List<dynamic>).map((dynamic e) => RecordingModel.fromJson(e as Map<String, dynamic>)).toList(),
      ),
    );
  }

  Future<RepoResult> deleteRecording(String recordingId) async {
    return await executeAndReturnResult(
      () => profileService.deleteRecording(recordingId),
      (json) async => RepoResult.success(json["data"] as Map<String, dynamic>),
    );
  }

  Future<RepoResult> deleteAccount() async {
    return await executeAndReturnResult(
      () => profileService.deleteAccount(),
      (json) async => RepoResult.success(json),
    );
  }

  Future<RepoResult> saveAsFinal(String recordingId) async {
    return await executeAndReturnResult(
      () => profileService.saveAsFinal(recordingId),
      (json) async => RepoResult.success(json),
    );
  }
}

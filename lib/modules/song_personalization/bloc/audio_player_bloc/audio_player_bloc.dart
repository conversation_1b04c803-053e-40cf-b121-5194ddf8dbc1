import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:juce_mix_player/juce_mix_player.dart';
import 'package:just_audio/just_audio.dart';
import 'package:melodyze/core/services/file_manager/file_manager.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/path_wrapper.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/song_personalization/models/annotated_data/annotated_data.dart';

part 'audio_player_event.dart';
part 'audio_player_state.dart';

enum MixerTrackType {
  bgm,
  guide,
  vocal;

  String createString() {
    return toString().split('.').last;
  }
}

class AudioPlayerBloc extends SafeBloc<AudioPlayerEvent, AudioPlayerState> {
  static const tag = "AudioPlayerBloc";
  final AudioPlayer _audioPlayer = AudioPlayer();
  final JuceMixer mixer = JuceMixer();
  final Debouncer _debouncer = Debouncer();
  MixerComposeModel? mixComposeModel;
  bool isFilesDownloaded = false;

  AppLifecycleListener? _listener;
  AudioPlayerLoadEvent? lastLoadEvent;
  String? lastAnnotationId;
  bool? _wasPlayingOnHide;

  String? downloadedBgmPath;
  String? downloadedGuidePath;
  bool isMetronomeEnabled = false;
  bool isGuideEnabled = false;
  double metronomeVol = 0.0;
  double guideVol = 0.0;

  Duration get position => _audioPlayer.position;
  bool get isPlaying => _audioPlayer.playing;
  Duration get audioDuration => _audioPlayer.duration ?? Duration.zero;

  AudioPlayerBloc() : super(AudioPlayerInitial()) {
    _setupEventHandlers();
    _setupAudioListeners();
    _setupLifecycleListener();
  }

  @override
  Future<void> close() async {
    logger.d("$tag: closed");
    await _audioPlayer.dispose();
    _listener?.dispose();
    _debouncer.dispose();
    mixer.dispose();
    return super.close();
  }

  void _setupEventHandlers() {
    on<AudioPlayerLoadEvent>(_handleLoadEvent);
    on<AudioPlayerPlayEvent>(_handlePlayEvent);
    on<AudioPlayerPauseEvent>(_handlePauseEvent);
    on<AudioPlayerStopEvent>(_handleStopEvent);
    on<AudioPlayerProgressUpdateEvent>(_handleProgressUpdateEvent);
    on<AudioPlayerSeekEvent>(_handleSeekEvent);
    on<ToggleMetronomeEvent>(_onToggleMetronome);
    on<ToggleGuideEvent>(_onToggleGuide);
    on<DownloadFilesEvent>(_onDownloadFiles);
    on<PrepareMixerEvent>(_onPrepareMixer);
  }

  void _setupAudioListeners() {
    _audioPlayer.playerStateStream.listen((state) {
      if (state.processingState == ProcessingState.completed) {
        add(AudioPlayerStopEvent());
      }
    });

    _audioPlayer.positionStream.listen((position) {
      if (!isClosed) {
        add(AudioPlayerProgressUpdateEvent(position: position));
      }
    });
  }

  void _setupLifecycleListener() {
    _listener = AppLifecycleListener(
      onHide: () {
        _wasPlayingOnHide = isPlaying;
        add(AudioPlayerPauseEvent());
      },
      onShow: () {
        if (_wasPlayingOnHide == true) {
          add(AudioPlayerPlayEvent());
        }
      },
    );
  }

  Future<void> _handleLoadEvent(AudioPlayerLoadEvent event, _) async {
    try {
      final playingState = _audioPlayer.playing;
      final lastSeekPosition = _audioPlayer.position;
      if (_audioPlayer.playing) {
        await _audioPlayer.stop();
      }

      add(ToggleGuideEvent(enable: false, volume: guideVol, showToast: false));
      add(ToggleMetronomeEvent(enable: false, volume: metronomeVol, showToast: false));

      await _audioPlayer.setUrl(event.annotatedData.songPath);

      if (lastAnnotationId == event.annotatedData.id) {
        await _audioPlayer.seek(lastSeekPosition);
      }

      if (lastAnnotationId != event.annotatedData.id) {
        mixComposeModel = null;
      }

      lastAnnotationId = event.annotatedData.id;
      isFilesDownloaded = false;
      emit(AudioPlayerLoaded());
      if ((lastLoadEvent == null && event.autoPlay) || playingState) {
        add(AudioPlayerPlayEvent());
      }
      lastLoadEvent = event;
      add(DownloadFilesEvent(
        annotatedData: event.annotatedData,
        timeSign: event.song.timeSignature,
        duration: audioDuration.inSeconds.toDouble(),
      ));
    } catch (e) {
      logger.e(tag, error: e);
      emit(AudioPlayerError(error: "Error loading audio"));
    }
  }

  Future<void> _onDownloadFiles(DownloadFilesEvent event, _) async {
    try {
      final bgmUrl = event.annotatedData.songPath;
      final guideUrl = event.annotatedData.guideVocalPath;

      final downloads = <Future<FileOperationResult>>[
        FileManagerQuickAccess.download(bgmUrl, FileType.bgm),
        if (!guideUrl.isNullOrEmpty) FileManagerQuickAccess.download(guideUrl!, FileType.guide),
      ];

      final results = await Future.wait(downloads);

      // Extract paths from successful downloads
      if (results[0] is FileOperationSuccess) {
        downloadedBgmPath = (results[0] as FileOperationSuccess).localPath;
      }

      if (results.length > 1 && results[1] is FileOperationSuccess) {
        downloadedGuidePath = (results[1] as FileOperationSuccess).localPath;
      }

      add(PrepareMixerEvent(
        annotatedData: event.annotatedData,
        timeSign: event.timeSign,
        duration: event.duration,
      ));
    } catch (e) {
      logger.e('$tag: Error downloading audio files', error: e);
    }
  }

  Future<void> _onPrepareMixer(PrepareMixerEvent event, _) async {
    List<MixerTrack> mixTracks = [];
    mixTracks.add(MixerTrack(id: "bgm", path: downloadedBgmPath, enabled: true));

    final metTracks = mixer.createMetronomeTracks(
      tempo: int.tryParse(event.annotatedData.tempo) ?? 0,
      timeSignature: event.timeSign,
      volume: 1.0,
      hPath: await AssetPaths.extractAsset("assets/metronome_tone/met_h.wav"),
      lPath: await AssetPaths.extractAsset("assets/metronome_tone/met_l.wav"),
      enabled: false,
    );

    mixTracks.addAll(metTracks);

    if (!downloadedGuidePath.isNullOrEmpty) {
      mixTracks.add(MixerTrack(id: "guide", path: downloadedGuidePath, enabled: false, volume: 1.0));
    }

    mixComposeModel = MixerComposeModel(tracks: mixTracks, outputDuration: event.duration);
    isFilesDownloaded = true;
    emit(AudioPlayerFilesDownloaded());
  }

  Future<void> _handlePlayEvent(AudioPlayerPlayEvent event, _) async {
    emit(AudioPlayerPlaying(startTime: DateTime.now()));
    unawaited(_audioPlayer.play());
  }

  Future<void> _handlePauseEvent(AudioPlayerPauseEvent event, _) async {
    await _audioPlayer.pause();
    emit(AudioPlayerPaused());
  }

  Future<void> _handleStopEvent(AudioPlayerStopEvent event, _) async {
    await _audioPlayer.stop();
    await _audioPlayer.seek(Duration.zero);
    _wasPlayingOnHide = false;
    emit(AudioPlayerStopped());
  }

  void _handleProgressUpdateEvent(AudioPlayerProgressUpdateEvent event, _) {
    emit(AudioPlayerProgressUpdated(position: event.position));
  }

  Future<void> _handleSeekEvent(AudioPlayerSeekEvent event, _) async {
    _debouncer.debounce(() async {
      Duration seekPosition = Duration(milliseconds: event.milliseconds.toInt());
      await _audioPlayer.seek(seekPosition);
    }, 30);
  }

  Future<void> _onToggleMetronome(ToggleMetronomeEvent event, _) async {
    logger.i("_onToggleMetronome: ${event.enable} | ${event.volume}");
    mixComposeModel = mixComposeModel?.copyWith(
      tracks: mixComposeModel?.tracks?.map((track) {
        if (track.id.startsWith("metronome")) {
          return track.copyWith(enabled: event.enable, volume: event.volume);
        } else {
          return track;
        }
      }).toList(),
    );
    isMetronomeEnabled = event.enable;
    await handleAudioMix();
    if (event.showToast) {
      unawaited(DI().resolve<AppToast>().showToast('Metronome ${event.enable ? "enabled" : "disabled"}'));
    }
  }

  Future<void> _onToggleGuide(ToggleGuideEvent event, _) async {
    logger.i("_onToggleGuide: ${event.enable} | ${event.volume}");
    mixComposeModel = mixComposeModel?.copyWith(
      tracks: mixComposeModel?.tracks?.map((track) {
        if (track.id == "guide") {
          return track.copyWith(enabled: event.enable, volume: event.volume);
        } else {
          return track;
        }
      }).toList(),
    );
    guideVol = event.volume;
    isGuideEnabled = event.enable;
    await handleAudioMix();
    if (event.showToast) {
      unawaited(DI().resolve<AppToast>().showToast('Guide ${event.enable ? "enabled" : "disabled"}'));
    }
  }

  Future<void> handleAudioMix() async {
    try {
      if (mixComposeModel == null) {
        return;
      }
      final mixedAudioPath = await PathWrapper.getJuceMixerExportPath();
      mixComposeModel?.output = mixedAudioPath;
      await mixer.export(mixComposeModel!);

      final lastPosition = _audioPlayer.position;
      await _audioPlayer.setFilePath(mixedAudioPath);
      await _audioPlayer.seek(lastPosition);
    } catch (e) {
      emitError('Error creating mix', error: e);
    }
  }

  void setPosition(Duration value) {
    _audioPlayer.seek(value);
  }

  void emitError(String message, {dynamic error}) {
    logger.e('$tag: $message', error: error);
    emit(AudioPlayerError(error: '$message \n ${error ?? ''}'));
    add(AudioPlayerStopEvent());
  }
}



  /// Runs [heavyOperation] after [delay] and emits [AudioPlayerInitial] if
  /// [heavyOperation] doesn't complete within [delay].
  ///
  /// [heavyOperation] is an operation that is expected to take more than
  /// [delay] milliseconds to complete. If [heavyOperation] completes
  /// successfully, [operationInProgress] is set to false and the loading timer
  /// is cancelled. If [heavyOperation] throws an error, [operationInProgress]
  /// is set to false and the error is rethrown.
  ///
  /// [delay] defaults to 100 milliseconds.
  ///
  /// This function is intended to be used for operations that are expected to
  /// take more than 100 milliseconds to complete. It prevents the UI from
  /// freezing while the operation is running.
  ///
  /// Note that if [heavyOperation] completes before [delay] milliseconds have
  /// passed, the loading timer is cancelled and [operationInProgress] is set
  /// to false.
  // Future<void> withDelayedLoading(
  //   Future<void> Function() heavyOperation, {
  //   Duration delay = const Duration(milliseconds: 300),
  // }) async {
  //   if (operationInProgress) {
  //     return;
  //   }
  //   operationInProgress = true;
  //   logger.d("$tag: Starting withDelayedLoading with delay: $delay");
  //   Timer? loadingTimer = Timer(delay, () {
  //     logger.d("$tag: Emitting AudioPlayerInitial due to delay");
  //     emit(AudioPlayerInitial());
  //   });

  //   try {
  //     await heavyOperation();
  //     if (loadingTimer.isActive) {
  //       loadingTimer.cancel();
  //     }
  //     logger.d("$tag: Heavy operation completed successfully");
  //     operationInProgress = false;
  //     return;
  //   } catch (e) {
  //     if (loadingTimer.isActive) {
  //       loadingTimer.cancel();
  //     }
  //     logger.e("$tag: Error during heavy operation", error: e);
  //     operationInProgress = false;
  //     rethrow;
  //   }
  // }
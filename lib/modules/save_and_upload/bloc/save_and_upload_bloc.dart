import 'dart:async';
import 'dart:io';

import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/services/file_manager/file_manager.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/device_info_wrapper.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/path_wrapper.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';
import 'package:melodyze/modules/share/share_data_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/juce_mix_player/juce_mix_bloc.dart';
import 'package:melodyze/modules/vocal_filters/bloc/vocal_filters_bloc.dart';

part 'save_and_upload_event.dart';
part 'save_and_upload_state.dart';

class SaveAndUploadBloc extends SafeBloc<SaveAndUploadEvent, SaveAndUploadState> {
  final String? userId;
  final ShareDataBloc shareDataBloc;
  final VocalfiltersBloc vocalfiltersBloc;
  final JuceMixBloc juceMixBloc;
  final juceKitWrapper = JuceKitWrapper();

  SaveAndUploadBloc({
    required this.shareDataBloc,
    required this.vocalfiltersBloc,
    required this.juceMixBloc,
    this.userId,
  }) : super(SaveAndUploadInitial()) {
    on<UploadEvent>(_upload);
    add(UploadEvent());
  }

  @override
  Future<void> close() async {
    unawaited(super.close());
  }

  FutureOr<void> _upload(UploadEvent event, _) async {
    emit(UploadProcessingState());
    try {
      final userDevice = await DeviceInfoWrapper.getSystemInfo();
      final finalRecordFlacPath = await PathWrapper.getFinalRecordFlacPath();

      // Extra delay added give time for juce to finish setMixedData to avoid juce export error
      await Future.delayed(const Duration(seconds: 2));
      await juceMixBloc.exportComposedAudio(finalRecordFlacPath);
      if (!File(finalRecordFlacPath).existsSync()) {
        emit(UploadFailureState("Failed to export composed audio"));
        return;
      }

      final uploadResult = await FileManagerQuickAccess.upload(
        finalRecordFlacPath,
        Endpoints.getUploadFinalMixedAudioSignedUrl,
        FileType.others,
      );

      if (uploadResult is FileOperationError) {
        emit(UploadFailureState(uploadResult.message));
        return;
      }

      final recordingBody = {
        "title": shareDataBloc.song!.title,
        "master_song_id": shareDataBloc.song!.id,
        "genre": shareDataBloc.annotatedData!.genre,
        "genre_id": shareDataBloc.annotatedData!.genreId,
        "scale": shareDataBloc.annotatedData!.scale,
        "tempo": shareDataBloc.annotatedData!.tempo,
        "input_mic": shareDataBloc.inputMic,
        "vocal_volume": vocalfiltersBloc.vocalVolume,
        "bgm_volume": vocalfiltersBloc.musicVolume,
        "vocal_filter_name": vocalfiltersBloc.vocalFilterName,
        "master_filter_name": vocalfiltersBloc.masterFilterName,
        "device": userDevice.device,
        "os": userDevice.os,
        "raw_audio_file_path": vocalfiltersBloc.recordedVocalS3path,
        "final_mixed_audio_path": (uploadResult as FileOperationSuccess).localPath, // s3 path
        "latency": vocalfiltersBloc.userGivenDelay,
        "denoise": false,
      };

      final response = await DI().resolve<ApiClient>().post(Endpoints.saveRecording, body: recordingBody);
      if (response?['success'] == true) {
        emit(UploadSuccessState());
      } else {
        emit(UploadFailureState("Failed to upload recording"));
      }
    } catch (error) {
      logger.e("Error saving recording: ${error.toString()}");
      emit(UploadFailureState("Failed to upload recording"));
    } finally {
      await juceMixBloc.close();
    }
  }
}

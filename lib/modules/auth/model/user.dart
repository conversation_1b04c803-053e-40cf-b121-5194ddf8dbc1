import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:melodyze/core/wrappers/device_info_wrapper.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
class User with _$User {
  const factory User({
    @JsonKey(name: '_id') required String id,
    @JsonKey(name: 'email_id') @Default('') String email,
    @Json<PERSON>ey(name: 'user_name') @Default('') String username,
    String? googlePhotoURL,
    @Json<PERSON>ey(name: 'preferences') @Default(Preferences()) Preferences preferences,
    @JsonKey(name: 'device_info') @Default(UserDeviceInfo()) UserDeviceInfo deviceInfo,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

@freezed
class Preferences with _$Preferences {
  const factory Preferences({
    @JsonKey(name: 'genres') @Default([]) List<String> genres,
    @J<PERSON>Key(name: 'artists') @Default([]) List<String> artists,
  }) = _Preferences;

  factory Preferences.fromJson(Map<String, dynamic> json) => _$PreferencesFromJson(json);
}

@freezed
class UserDeviceInfo with _$UserDeviceInfo {
  const factory UserDeviceInfo({
    @JsonKey(name: 'platform') @Default('') String platform,
    @JsonKey(name: 'device') @Default('') String device,
    @JsonKey(name: 'os') @Default('') String os,
    @JsonKey(name: 'uid') @Default('') String uid,
    @JsonKey(name: 'os_version') @Default('') String osVersion,
    @JsonKey(name: 'brand') @Default('') String brand,
  }) = _UserDeviceInfo;

  factory UserDeviceInfo.fromJson(Map<String, dynamic> json) => _$UserDeviceInfoFromJson(json);
}
